import os
import requests
import dashscope

def read_text_from_file(file_path):
    """从txt文件读取文本内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read().strip()
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        return None
    except Exception as e:
        print(f"读取文件时出错：{str(e)}")
        return None

# 指定txt文件路径
txt_file_path = "1.txt"  # 请修改为您的txt文件路径

# 从txt文件获取文本内容
text = read_text_from_file(txt_file_path)

# 检查是否成功读取文本
if text is None:
    print("无法读取文本文件，程序退出")
    exit(1)

def split_text(text, max_length=500):
    """将文本按指定长度切割成多个片段"""
    chunks = []
    for i in range(0, len(text), max_length):
        chunks.append(text[i:i + max_length])
    return chunks

def download_audio(audio_url, save_path):
    """下载音频文件"""
    try:
        response = requests.get(audio_url)
        response.raise_for_status()  # 检查请求是否成功
        with open(save_path, 'wb') as f:
            f.write(response.content)
        print(f"音频文件已保存至：{save_path}")
        return True
    except Exception as e:
        print(f"下载失败：{str(e)}")
        return False

def text_to_speech(text_chunk, chunk_index):
    """将文本片段转换为语音"""
    try:
        response = dashscope.audio.qwen_tts.SpeechSynthesizer.call(
            model="qwen-tts",
            api_key="sk-da706dede3ff465c986b539e83896606",
            text=text_chunk,
            voice="Ethan",
        )
        audio_url = response.output.audio["url"]
        save_path = f"downloaded_audio_{chunk_index + 1}.wav"  # 为每个片段创建不同的文件名

        if download_audio(audio_url, save_path):
            return save_path
        return None
    except Exception as e:
        print(f"TTS转换失败（片段 {chunk_index + 1}）：{str(e)}")
        return None

# 将文本切割成500字的片段
text_chunks = split_text(text, 500)
print(f"文本已切割成 {len(text_chunks)} 个片段")

# 循环处理每个文本片段
audio_files = []
for i, chunk in enumerate(text_chunks):
    print(f"正在处理第 {i + 1}/{len(text_chunks)} 个片段...")
    print(f"片段内容预览：{chunk[:50]}...")

    audio_file = text_to_speech(chunk, i)
    if audio_file:
        audio_files.append(audio_file)
    else:
        print(f"第 {i + 1} 个片段处理失败")

print(f"\n处理完成！成功生成 {len(audio_files)} 个音频文件：")
for audio_file in audio_files:
    print(f"- {audio_file}")